{"queries": {"rateSheet": {"queryKey": "rateSheet1", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "excludesArray": [], "includesArray": []}}, "deviations": {"queryKey": "deviations", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null}}, "planDesign": {"queryKey": "planDesign", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "excludesArray": [], "includesArray": []}}, "ratesPerEE": {"queryKey": "ratesPerEE", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "excludesArray": [], "includesArray": [], "numberOfEmployees": null}}, "customerData": {"queryKey": "customerData", "queryType": "SQL", "parameters": {"planUuid": null}}, "cuttingChase": {"queryKey": "cuttingChase", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null}}, "planOverview": {"queryKey": "planOverview", "queryType": "SQL", "parameters": {"includesArray": []}}, "renewalCharges": {"queryKey": "renewalCharges", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null}}, "targetedClaims": {"queryKey": "targetedClaims", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null}}, "otherConsiderations": {"queryKey": "otherConsiderations", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null}}}}