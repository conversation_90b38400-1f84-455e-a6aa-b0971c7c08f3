   <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION sandf.fn_get_renewal_charges_and_targeted_claims_RTQ(
    target_plan_uuid TEXT,
    user_id_param TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    v_plan_id INTEGER;
    v_employee_class_id INTEGER;
    quote_record RECORD;
    quote_json JSONB;
    carrier_name TEXT;
    carrier_list TEXT[] := '{}';
    section_list JSONB := '[]'::JSONB;
    renewal_section_list JSONB := '[]'::JSONB;

    -- Premium and volume variables
    ehc_prem NUMERIC := 0;
    ehc_vol NUMERIC := 0;
    dental_prem NUMERIC := 0;
    dental_vol NUMERIC := 0;
    ehc_avg NUMERIC := 0;
    dental_avg NUMERIC := 0;

    -- Rating factor variables
    trend_ehc NUMERIC := 0;
    ibnr_ehc NUMERIC := 0;
    tlr_ehc NUMERIC := 0;
    ibnr_dental NUMERIC := 0;
    tlr_dental NUMERIC := 0;
    amount NUMERIC := 0;
    renewal_percent NUMERIC := 0;

    -- Text variables for display
    trend_ehc_text TEXT;
    ibnr_ehc_text TEXT;
    tlr_ehc_text TEXT;
    ibnr_dental_text TEXT;
    tlr_dental_text TEXT;
    amount_text TEXT;
    renewal_percent_str TEXT;
    x RECORD;

    -- Helper function for safe numeric conversion
    safe_numeric_value NUMERIC;
BEGIN
    -- Get plan_id
    SELECT p.plan_id INTO v_plan_id
    FROM sandf.plan p
    WHERE p.plan_uuid = target_plan_uuid::uuid
    ORDER BY p.plan_id ASC
    LIMIT 1;

    IF v_plan_id IS NULL THEN
        RETURN jsonb_build_object('carriers', ARRAY[]::TEXT[], 'sections', '[]'::JSONB);
    END IF;

    -- Get first RTQ employee_class_id
    SELECT employee_class_id INTO v_employee_class_id
    FROM sandf.employee_class
    WHERE plan_id = v_plan_id AND rtq_set_flag = TRUE
    ORDER BY employee_class_id ASC
    LIMIT 1;

    IF v_employee_class_id IS NULL THEN
        RETURN jsonb_build_object('carriers', ARRAY[]::TEXT[], 'sections', '[]'::JSONB);
    END IF;

    -- Process quotes in order of user preference
    FOR quote_record IN
        SELECT ecq.*, q.quote_id, q.quote_uuid, c.description as carrier_name
        FROM sandf.employee_class_quote ecq
        JOIN sandf.quote q ON ecq.quote_id = q.quote_id
        JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
        WHERE ecq.employee_class_id = v_employee_class_id
        ORDER BY
            sandf.get_user_preference_order(
                user_id_param,
                target_plan_uuid,
                q.quote_id,
                q.quote_uuid,
                999999
            ) ASC,
            c.description ASC
    LOOP
        quote_json := quote_record.formatted_quote_details::jsonb;
        carrier_name := quote_record.carrier_name;
        carrier_list := array_append(carrier_list, carrier_name);

        -- Reset totals for each carrier
        ehc_prem := 0;
        ehc_vol := 0;
        dental_prem := 0;
        dental_vol := 0;

        -- Extract rating factors with improved null/empty/zero handling
        trend_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,pooling}';
        BEGIN
            trend_ehc := CASE
                WHEN trend_ehc_text IS NULL OR TRIM(trend_ehc_text) = '' OR TRIM(trend_ehc_text) = '0' THEN 0
                ELSE COALESCE((regexp_replace(TRIM(trend_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                trend_ehc := 0;
        END;

        ibnr_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,ibnr}';
        BEGIN
            ibnr_ehc := CASE
                WHEN ibnr_ehc_text IS NULL OR TRIM(ibnr_ehc_text) = '' OR TRIM(ibnr_ehc_text) = '0' THEN 0
                ELSE COALESCE((regexp_replace(TRIM(ibnr_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                ibnr_ehc := 0;
        END;

        tlr_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,targetLossRatio}';
        BEGIN
            tlr_ehc := CASE
                WHEN tlr_ehc_text IS NULL OR TRIM(tlr_ehc_text) = '' OR TRIM(tlr_ehc_text) = '0' THEN 0
                ELSE 100 - COALESCE((regexp_replace(TRIM(tlr_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                tlr_ehc := 0;
        END;

        ibnr_dental_text := quote_json #>> '{benefitPremiums,ratingFactors,dentalRatingFactors,ibnr}';
        BEGIN
            ibnr_dental := CASE
                WHEN ibnr_dental_text IS NULL OR TRIM(ibnr_dental_text) = '' OR TRIM(ibnr_dental_text) = '0' THEN 0
                ELSE COALESCE((regexp_replace(TRIM(ibnr_dental_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                ibnr_dental := 0;
        END;

        tlr_dental_text := quote_json #>> '{benefitPremiums,ratingFactors,dentalRatingFactors,targetLossRatio}';
        BEGIN
            tlr_dental := CASE
                WHEN tlr_dental_text IS NULL OR TRIM(tlr_dental_text) = '' OR TRIM(tlr_dental_text) = '0' THEN 0
                ELSE 100 - COALESCE((regexp_replace(TRIM(tlr_dental_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                tlr_dental := 0;
        END;

        -- Calculate EHC volumes and premiums with safe conversion
        FOR x IN SELECT * FROM jsonb_each(quote_json #> '{benefitPremiums,extendedHealthPremium}')
        LOOP
            BEGIN
                safe_numeric_value := CASE
                    WHEN x.value ->> 'volume' IS NULL OR TRIM(x.value ->> 'volume') = '' THEN 0
                    ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'volume'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                END;
                ehc_vol := ehc_vol + safe_numeric_value;
            EXCEPTION
                WHEN OTHERS THEN
                    ehc_vol := ehc_vol + 0;
            END;

            BEGIN
                safe_numeric_value := CASE
                    WHEN x.value ->> 'premium' IS NULL OR TRIM(x.value ->> 'premium') = '' THEN 0
                    ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'premium'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                END;
                ehc_prem := ehc_prem + safe_numeric_value;
            EXCEPTION
                WHEN OTHERS THEN
                    ehc_prem := ehc_prem + 0;
            END;
        END LOOP;

        RAISE NOTICE 'EHC --: %, %', ehc_vol, ehc_prem;

        -- Calculate Dental volumes and premiums with safe conversion
        FOR x IN SELECT * FROM jsonb_each(quote_json #> '{benefitPremiums,dentalCarePremium}')
        LOOP
            BEGIN
                safe_numeric_value := CASE
                    WHEN x.value ->> 'volume' IS NULL OR TRIM(x.value ->> 'volume') = '' THEN 0
                    ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'volume'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                END;
                dental_vol := dental_vol + safe_numeric_value;
            EXCEPTION
                WHEN OTHERS THEN
                    dental_vol := dental_vol + 0;
            END;

            BEGIN
                safe_numeric_value := CASE
                    WHEN x.value ->> 'premium' IS NULL OR TRIM(x.value ->> 'premium') = '' THEN 0
                    ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'premium'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                END;
                dental_prem := dental_prem + safe_numeric_value;
            EXCEPTION
                WHEN OTHERS THEN
                    dental_prem := dental_prem + 0;
            END;
        END LOOP;

        RAISE NOTICE 'Dental --: %, %', dental_vol, dental_prem;

        -- Calculate average premiums per unit volume (monthly * 12)
        ehc_avg := CASE WHEN ehc_vol > 0 THEN (ehc_prem / ehc_vol) * 12 ELSE 0 END;
        dental_avg := CASE WHEN dental_vol > 0 THEN (dental_prem / dental_vol) * 12 ELSE 0 END;

        RAISE NOTICE 'ehc_avg --: %', ehc_avg;
        RAISE NOTICE 'dental_avg --: %', dental_avg;

        -- Calculate targetClaimsValue with renewal charges
        -- Now using 0 instead of NULL checks since we default to 0
        IF (ehc_avg = 0 AND dental_avg = 0) THEN
            amount_text := '0';
        ELSE
            BEGIN
                amount := (ehc_avg * (1 - (trend_ehc / 100)) * (1 - (ibnr_ehc / 100)) * (1 - (tlr_ehc / 100))) +
                         (dental_avg * (1 - (ibnr_dental / 100)) * (1 - (tlr_dental / 100)));
                RAISE NOTICE 'amount --: %', amount;
                amount_text := COALESCE(ROUND(amount::NUMERIC, 2)::TEXT, '0');
            EXCEPTION
                WHEN OTHERS THEN
                    amount_text := '0';
                    amount := 0;
            END;
        END IF;

        RAISE NOTICE 'amount_text --: %', amount_text;

        -- Add to section lists
        section_list := section_list || jsonb_build_object('amount', amount_text);

        -- Calculate and format renewal charge percentage
        IF amount_text = '0' OR (ehc_avg + dental_avg) = 0 THEN
            renewal_percent_str := '0%';
        ELSE
            BEGIN
                renewal_percent := (1 - (amount / (ehc_avg + dental_avg))) * 100;
                renewal_percent_str := COALESCE(TO_CHAR(CEIL(renewal_percent * 10) / 10, 'FM999990.0'), '0') || '%';
            EXCEPTION
                WHEN OTHERS THEN
                    renewal_percent_str := '0%';
            END;
        END IF;

        renewal_section_list := renewal_section_list || jsonb_build_object(
            'renewalCharge', renewal_percent_str
        );
    END LOOP;

    -- Return final result
    RETURN jsonb_build_object(
        'targetedClaims', jsonb_build_object(
            'carriers', carrier_list,
            'sections', section_list
        ),
        'renewalCharges', jsonb_build_object(
            'carriers', carrier_list,
            'sections', renewal_section_list
        )
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Global error handler - return empty result
        RAISE NOTICE 'Error in function: %', SQLERRM;
        RETURN jsonb_build_object(
            'targetedClaims', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            ),
            'renewalCharges', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
END;
$$;

        ]]>
        </sql>