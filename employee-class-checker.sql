<sql splitStatements="false">
    <![CDATA[
                CREATE OR REPLACE FUNCTION sandf.fn_check_employee_class_type(
    plan_uuid_param TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    employee_class_count INTEGER;
    employee_classes TEXT[];
    has_rtq_only BOOLEAN := FALSE;
BEGIN
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    IF employee_class_count = 1 AND employee_classes[1] = 'RTQ' THEN
        has_rtq_only := TRUE;
    END IF;

    RETURN has_rtq_only;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error checking employee class type: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
END;
$$;
]]>
        </sql>
